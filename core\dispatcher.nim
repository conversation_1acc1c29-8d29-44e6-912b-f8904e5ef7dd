import json, algorithm, strutils, asyncdispatch
import utils/log, bot, ws
from matcher import globalMatchers, Matcher

type
  Dispatcher* = ref object

proc handle*(dispatcher: Dispatcher, event: JsonNode,
    conn: WebSocket) {.async, gcsafe.} =
  if event{"post_type"}.getStr() == "message" and event{"message_type"}.getStr() != "group":
    return

  let botInstance = newBot(conn)

  globalMatchers.sort(proc(a, b: Matcher): int = cmp(a.priority, b.priority))

  var i = 0
  while i < globalMatchers.len:
    let m = globalMatchers[i]
    if m.eventType == "" or m.eventType == event{"post_type"}.getStr():
      if m.rule == nil or m.rule(event):
        try:
          await m.handler(botInstance, event)
        except:
          logError("处理器执行失败: " & getCurrentExceptionMsg())

        if m.temp:
          globalMatchers.delete(i)
          continue

        if m.`block`:
          break
    inc i

proc newDispatcher*(): Dispatcher = Dispatcher()





